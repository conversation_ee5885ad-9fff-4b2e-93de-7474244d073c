package ai.ad.webview.sdk.api.interfaces;

import android.app.Activity;
import android.app.Application;
import android.content.Context;

/**
 * WSKSDK 接口
 * 定义 WSKSDK 必须实现的方法
 */
public interface IWSKSDK {

    void initialize(Application application, String appId, IWSKCallback callback);

    /**
     * 获取应用ID
     *
     * @return 应用ID
     */
    String getAppId();

    /**
     * 获取SDK版本
     *
     * @return SDK版本
     */
    String version();

    /**
     * 获取设备ID
     *
     * @return 设备ID
     */
    String getDeviceId();

    void attach(Activity hostActivity);

    /**
     * 是否支持应用外显示
     *
     * @return 是否支持应用外显示
     */
    boolean isSupportOutApp();

    void detach(Activity context);


    /**
     * 通知WebView已加载
     */
    void notifyWebViewLoaded();

    void notifyScriptCompleted();

    /**
     * 通知错误
     *
     * @param errorMessage 错误信息
     */
    void notifyError(String errorMessage);

    /**
     * 创建Activity代理类实例
     *
     * @param context 上下文
     * @return IWSKActivity
     */
    IWSKActivity createActivityProxy(Context context);

    /**
     * 创建Service代理类实例
     *
     * @param context 上下文
     * @return IWSKService
     */
    IWSKService createServiceProxy(Context context);

    IInAppOverlayManager createInAppOverlayManager();

    /**
     * 是否已附加
     *
     * @return 是否已附加
     */
    boolean isAttach();

    /**
     * 检查更新
     * @param currentVersion 当前版本号
     * @param appId 应用包名
     * @param deviceId 设备ID
     * @param callback 回调接口
     */
    void checkUpdate(String currentVersion, String appId, String deviceId, IUpdateCallback callback);

    /**
     * 获取当前最新的dex文件路径
     * @return dex文件路径
     */
    String getLatestDexPath();
}
