package ai.ad.webview.hotupgrade;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import ai.ad.webview.plugin.dex.WSKResLoader;
import ai.ad.webview.sdk.WSKSDK;
import ai.ad.webview.sdk.logger.WSKLog;

/**
 * Hot Upgrade Tester主Activity
 * 提供测试动态加载dex功能的界面
 */
public class MainActivity extends Activity {
    private static final String TAG = "MainActivity";

    private ListView listViewDexFiles;
    private TextView textViewResult;
    private DexFileAdapter adapter;
    private List<DexFileInfo> dexFileList;
    private ExecutorService executorService;
    private Handler mainHandler;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        WSKLog.d(TAG, "onCreate -> Hot Upgrade Tester started");

        initViews();
        initData();
        loadDexFilesFromAssets();
    }

    private void initViews() {
        listViewDexFiles = findViewById(R.id.listview_dex_files);
        textViewResult = findViewById(R.id.textview_result);

        dexFileList = new ArrayList<>();
        adapter = new DexFileAdapter(this, dexFileList);
        listViewDexFiles.setAdapter(adapter);

        listViewDexFiles.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                DexFileInfo dexFileInfo = dexFileList.get(position);
                testDexFile(dexFileInfo);
            }
        });
    }

    private void initData() {
        executorService = Executors.newSingleThreadExecutor();
        mainHandler = new Handler(Looper.getMainLooper());
    }

    /**
     * 从assets目录加载所有.so文件
     */
    private void loadDexFilesFromAssets() {
        WSKLog.d(TAG, "loadDexFilesFromAssets -> Loading dex files from assets");

        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    String[] assetFiles = getAssets().list("");
                    List<DexFileInfo> tempList = new ArrayList<>();

                    if (assetFiles != null) {
                        for (String fileName : assetFiles) {
                            if (fileName.endsWith(".so")) {
                                WSKLog.d(TAG, "loadDexFilesFromAssets -> Found dex file: " + fileName);
                                DexFileInfo dexFileInfo = new DexFileInfo(fileName);
                                tempList.add(dexFileInfo);

                                // 验证dex文件
                                verifyDexFile(dexFileInfo);
                            }
                        }
                    }

                    // 更新UI
                    mainHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            dexFileList.clear();
                            dexFileList.addAll(tempList);
                            adapter.notifyDataSetChanged();

                            textViewResult.setText("Found " + tempList.size() + " DEX files in assets. Click to test.");
                            WSKLog.i(TAG, "loadDexFilesFromAssets -> Loaded " + tempList.size() + " dex files");
                        }
                    });

                } catch (IOException e) {
                    WSKLog.e(TAG, "loadDexFilesFromAssets -> Error loading assets: " + e.getMessage());
                    mainHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            textViewResult.setText("Error loading assets: " + e.getMessage());
                        }
                    });
                }
            }
        });
    }

    /**
     * 验证dex文件的合法性
     */
    private void verifyDexFile(DexFileInfo dexFileInfo) {
        try {
            // 从assets复制文件到临时目录进行验证
            File tempFile = copyAssetToTemp(dexFileInfo.fileName);
            if (tempFile != null) {
                // 初始化WSKResLoader
                WSKResLoader.getInstance().init(this);
                boolean isValid = WSKResLoader.getInstance().verifyDex(tempFile);
                dexFileInfo.isValid = isValid;
                dexFileInfo.status = isValid ? "Valid DEX" : "Invalid DEX";

                // 清理临时文件
                tempFile.delete();

                WSKLog.d(TAG, "verifyDexFile -> " + dexFileInfo.fileName + " validation: " + isValid);
            } else {
                dexFileInfo.isValid = false;
                dexFileInfo.status = "Copy failed";
                WSKLog.e(TAG, "verifyDexFile -> Failed to copy " + dexFileInfo.fileName + " to temp");
            }
        } catch (Exception e) {
            dexFileInfo.isValid = false;
            dexFileInfo.status = "Error: " + e.getMessage();
            WSKLog.e(TAG, "verifyDexFile -> Error verifying " + dexFileInfo.fileName + ": " + e.getMessage());
        }

        // 更新UI
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                adapter.notifyDataSetChanged();
            }
        });
    }

    /**
     * 从assets复制文件到临时目录
     */
    private File copyAssetToTemp(String fileName) {
        try {
            File tempFile = new File(getCacheDir(), "temp_" + fileName);

            java.io.InputStream inputStream = getAssets().open(fileName);
            java.io.FileOutputStream outputStream = new java.io.FileOutputStream(tempFile);

            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }

            outputStream.close();
            inputStream.close();

            return tempFile;
        } catch (IOException e) {
            WSKLog.e(TAG, "copyAssetToTemp -> Error copying " + fileName + ": " + e.getMessage());
            return null;
        }
    }

    /**
     * 测试指定的dex文件
     */
    private void testDexFile(DexFileInfo dexFileInfo) {
        WSKLog.d(TAG, "testDexFile -> Testing dex file: " + dexFileInfo.fileName);

        textViewResult.setText("Testing " + dexFileInfo.fileName + "...");

        executorService.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    // 使用WSKSDK的test接口进行测试
                    String result = WSKSDK.test(dexFileInfo.fileName);

                    WSKLog.i(TAG, "testDexFile -> Test result for " + dexFileInfo.fileName + ": " + result);

                    // 更新UI显示结果
                    mainHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            textViewResult.setText("Test Result for " + dexFileInfo.fileName + ":\n\n" + result);

                            // 显示Toast提示
                            if (result.startsWith("Success")) {
                                Toast.makeText(MainActivity.this, "Test successful!", Toast.LENGTH_SHORT).show();
                            } else {
                                Toast.makeText(MainActivity.this, "Test failed!", Toast.LENGTH_SHORT).show();
                            }
                        }
                    });

                } catch (Exception e) {
                    String errorMsg = "Exception during test: " + e.getMessage();
                    WSKLog.e(TAG, "testDexFile -> " + errorMsg);

                    mainHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            textViewResult.setText("Test Error for " + dexFileInfo.fileName + ":\n\n" + errorMsg);
                            Toast.makeText(MainActivity.this, "Test error!", Toast.LENGTH_SHORT).show();
                        }
                    });
                }
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }

    /**
     * Dex文件信息数据类
     */
    private static class DexFileInfo {
        String fileName;
        boolean isValid;
        String status;

        public DexFileInfo(String fileName) {
            this.fileName = fileName;
            this.isValid = false;
            this.status = "Checking...";
        }
    }

    /**
     * ListView适配器
     */
    private static class DexFileAdapter extends BaseAdapter {
        private Context context;
        private List<DexFileInfo> dexFileList;
        private LayoutInflater inflater;

        public DexFileAdapter(Context context, List<DexFileInfo> dexFileList) {
            this.context = context;
            this.dexFileList = dexFileList;
            this.inflater = LayoutInflater.from(context);
        }

        @Override
        public int getCount() {
            return dexFileList.size();
        }

        @Override
        public Object getItem(int position) {
            return dexFileList.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder;

            if (convertView == null) {
                convertView = inflater.inflate(R.layout.item_dex_file, parent, false);
                holder = new ViewHolder();
                holder.textViewFileName = convertView.findViewById(R.id.textview_filename);
                holder.textViewStatus = convertView.findViewById(R.id.textview_status);
                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
            }

            DexFileInfo dexFileInfo = dexFileList.get(position);
            holder.textViewFileName.setText(dexFileInfo.fileName);
            holder.textViewStatus.setText(dexFileInfo.status);

            // 根据验证状态设置颜色
            if (dexFileInfo.status.equals("Checking...")) {
                holder.textViewStatus.setTextColor(0xFF666666); // 灰色
            } else if (dexFileInfo.isValid) {
                holder.textViewStatus.setTextColor(0xFF4CAF50); // 绿色
            } else {
                holder.textViewStatus.setTextColor(0xFFF44336); // 红色
            }

            return convertView;
        }

        private static class ViewHolder {
            TextView textViewFileName;
            TextView textViewStatus;
        }
    }
}