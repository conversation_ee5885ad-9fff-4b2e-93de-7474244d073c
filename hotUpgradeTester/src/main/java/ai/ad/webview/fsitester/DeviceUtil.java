package ai.ad.webview.fsitester;

import android.app.KeyguardManager;
import android.content.Context;
import android.os.Build;
import android.os.PowerManager;

/**
 * 设备工具类
 * 提供设备状态检测功能
 */
public class DeviceUtil {
    private static final String TAG = "DeviceUtil";
    
    /**
     * 检查设备是否处于锁屏状态
     * 
     * @param context 上下文
     * @return 是否锁屏
     */
    public static boolean isKeyguardLocked(Context context) {
        try {
            KeyguardManager keyguardManager = (KeyguardManager) context.getSystemService(Context.KEYGUARD_SERVICE);
            if (keyguardManager != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
                    return keyguardManager.isDeviceLocked();
                } else {
                    return keyguardManager.inKeyguardRestrictedInputMode();
                }
            }
        } catch (Exception e) {
            WSKLog.e(TAG, "isKeyguardLocked -> Error checking keyguard status: " + e.getMessage());
        }
        return false;
    }
    
    /**
     * 检查屏幕是否处于交互状态（亮屏且用户可交互）
     * 
     * @param context 上下文
     * @return 是否处于交互状态
     */
    public static boolean isInteractive(Context context) {
        try {
            PowerManager powerManager = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
            if (powerManager != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT_WATCH) {
                    return powerManager.isInteractive();
                } else {
                    return powerManager.isScreenOn();
                }
            }
        } catch (Exception e) {
            WSKLog.e(TAG, "isInteractive -> Error checking interactive status: " + e.getMessage());
        }
        return false;
    }
    
    /**
     * 检查是否为三星设备
     * 
     * @return 是否为三星设备
     */
    public static boolean isSamsung() {
        return "samsung".equalsIgnoreCase(Build.MANUFACTURER);
    }
    
    /**
     * 获取设备制造商信息
     * 
     * @return 设备制造商
     */
    public static String getManufacturer() {
        return Build.MANUFACTURER;
    }
    
    /**
     * 获取设备型号信息
     * 
     * @return 设备型号
     */
    public static String getModel() {
        return Build.MODEL;
    }
    
    /**
     * 获取Android版本信息
     * 
     * @return Android版本
     */
    public static int getAndroidVersion() {
        return Build.VERSION.SDK_INT;
    }
}
